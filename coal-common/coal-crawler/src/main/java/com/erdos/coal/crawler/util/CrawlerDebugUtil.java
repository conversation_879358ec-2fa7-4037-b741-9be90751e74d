package com.erdos.coal.crawler.util;

import com.erdos.coal.crawler.constants.JavaScriptConstants;
import com.microsoft.playwright.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 爬虫调试工具类
 * 用于调试和测试爬虫功能
 */
public class CrawlerDebugUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(CrawlerDebugUtil.class);
    private static final String BASE_URL = "https://www.meiyibao.com/";
    
    /**
     * 调试页面元素
     */
    public static void debugPageElements() {
        try (Playwright playwright = Playwright.create()) {
            // 启动浏览器（可视化模式用于调试）
            Browser browser = playwright.chromium().launch(
                    new BrowserType.LaunchOptions()
                            .setHeadless(false) // 设置为false以便观察
                            .setSlowMo(1000) // 慢动作，便于观察
            );
            
            // 创建浏览器上下文
            BrowserContext context = browser.newContext(
                    new Browser.NewContextOptions()
                            .setUserAgent("Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36")
            );

            // 创建新页面
            Page page = context.newPage();
            
            System.out.println("正在访问: " + BASE_URL);
            page.navigate(BASE_URL);
            
            // 等待页面加载
            System.out.println("等待页面加载...");
            page.waitForSelector(".coal-ing", new Page.WaitForSelectorOptions().setTimeout(30_000));
            
            // 测试各个标签页
            testTabSwitching(page);
            
            // 获取页面元素信息
            analyzePageElements(page);
            
            System.out.println("调试完成，按回车键关闭浏览器...");
            System.in.read(); // 等待用户输入
            
            page.close();
            context.close();
            browser.close();
            
        } catch (Exception e) {
            logger.error("调试失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 测试标签页切换
     */
    private static void testTabSwitching(Page page) {
        try {
            System.out.println("\n=== 测试标签页切换 ===");
            
            // 测试点击各个标签
            String[] tabs = {"#tab-1", "#tab-2", "#tab-3"};
            String[] tabNames = {"神华外购", "CCI指数", "CCTD指数"};
            
            for (int i = 0; i < tabs.length; i++) {
                try {
                    System.out.println("点击标签: " + tabNames[i] + " (" + tabs[i] + ")");
                    page.querySelector(tabs[i]).click();
                    
                    // 等待内容加载
                    page.waitForTimeout(2000);
                    
                    // 获取当前标签页的内容
                    Locator coalIngElements = page.locator(".coal-ing");
                    int count = coalIngElements.count();
                    System.out.println("找到 " + count + " 个 coal-ing 元素");
                    
                    for (int j = 0; j < Math.min(count, 3); j++) {
                        String text = coalIngElements.nth(j).textContent();
                        System.out.println("  元素[" + j + "]: " + text.substring(0, Math.min(text.length(), 100)) + "...");
                    }
                    
                } catch (Exception e) {
                    System.err.println("测试标签 " + tabs[i] + " 失败: " + e.getMessage());
                }
            }
            
        } catch (Exception e) {
            logger.error("测试标签页切换失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 分析页面元素
     */
    private static void analyzePageElements(Page page) {
        try {
            System.out.println("\n=== 分析页面元素 ===");
            
            // 分析coal-ing元素
            analyzeCoalIngElements(page);
            
            // 分析其他可能的价格元素
            analyzeOtherPriceElements(page);
            
            // 分析图表元素
            analyzeChartElements(page);
            
        } catch (Exception e) {
            logger.error("分析页面元素失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 分析coal-ing元素
     */
    private static void analyzeCoalIngElements(Page page) {
        try {
            System.out.println("\n--- coal-ing 元素分析 ---");
            
            Locator coalIngElements = page.locator(".coal-ing");
            int count = coalIngElements.count();
            System.out.println("总共找到 " + count + " 个 coal-ing 元素");
            
            for (int i = 0; i < count; i++) {
                try {
                    Locator element = coalIngElements.nth(i);
                    String text = element.textContent();
                    String html = element.innerHTML();
                    
                    System.out.println("\n元素[" + i + "]:");
                    System.out.println("  文本内容: " + text);
                    System.out.println("  HTML长度: " + html.length());
                    
                    // 尝试提取价格和热值信息
                    if (text.contains("元") && text.contains("kCal")) {
                        System.out.println("  ✓ 包含价格和热值信息");
                    }
                    
                } catch (Exception e) {
                    System.err.println("  分析元素[" + i + "]失败: " + e.getMessage());
                }
            }
            
        } catch (Exception e) {
            logger.error("分析coal-ing元素失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 分析其他价格元素
     */
    private static void analyzeOtherPriceElements(Page page) {
        try {
            System.out.println("\n--- 其他价格元素分析 ---");
            
            String[] selectors = {
                ".price-card", ".index-card", ".coal-price", ".price-info",
                "[class*='price']", "[class*='coal']", "[class*='index']"
            };
            
            for (String selector : selectors) {
                try {
                    Locator elements = page.locator(selector);
                    int count = elements.count();
                    if (count > 0) {
                        System.out.println("选择器 " + selector + " 找到 " + count + " 个元素");
                    }
                } catch (Exception e) {
                    // 忽略选择器错误
                }
            }
            
        } catch (Exception e) {
            logger.error("分析其他价格元素失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 分析图表元素（增强版）
     */
    private static void analyzeChartElements(Page page) {
        try {
            System.out.println("\n=== 图表元素分析（增强版） ===");

            // 检查canvas元素
            Locator canvasElements = page.locator("canvas");
            System.out.println("找到 " + canvasElements.count() + " 个 canvas 元素");

            // 检查svg元素
            Locator svgElements = page.locator("svg");
            System.out.println("找到 " + svgElements.count() + " 个 svg 元素");

            // 检查各种图表库
            try {
                Object chartLibsInfo = page.evaluate(JavaScriptConstants.CHECK_ECHARTS_AVAILABLE);
                System.out.println("图表库信息: " + chartLibsInfo);
            } catch (Exception e) {
                System.err.println("图表库检查失败: " + e.getMessage());
            }

            // 使用增强版方法获取所有图表数据
            try {
                Object allChartData = page.evaluate(JavaScriptConstants.GET_ALL_CHART_DATA);
                System.out.println("=== 全面图表数据分析 ===");
                System.out.println(allChartData);

                // 解析并展示详细信息
                parseAndDisplayChartData(allChartData.toString());

            } catch (Exception e) {
                System.err.println("全面图表数据获取失败: " + e.getMessage());
            }

            // 分析页面中的特定煤炭指数元素
            analyzeCoalIndexElements(page);

        } catch (Exception e) {
            logger.error("分析图表元素失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 解析并展示图表数据
     */
    private static void parseAndDisplayChartData(String chartDataJson) {
        try {
            System.out.println("\n--- 图表数据详细分析 ---");

            // 这里可以使用JSON解析库来解析数据
            // 为了简化，先直接输出原始数据
            if (chartDataJson != null && !chartDataJson.trim().isEmpty() && !chartDataJson.equals("null")) {
                System.out.println("原始数据长度: " + chartDataJson.length());

                // 检查是否包含煤炭相关数据
                if (chartDataJson.contains("coal") || chartDataJson.contains("煤") ||
                    chartDataJson.contains("price") || chartDataJson.contains("价格") ||
                    chartDataJson.contains("index") || chartDataJson.contains("指数")) {
                    System.out.println("✓ 发现煤炭相关数据");
                }

                // 检查是否包含数值数据
                if (chartDataJson.matches(".*\\d{3,}.*")) {
                    System.out.println("✓ 发现数值数据");
                }

                // 输出前500个字符作为预览
                String preview = chartDataJson.length() > 500 ?
                    chartDataJson.substring(0, 500) + "..." : chartDataJson;
                System.out.println("数据预览: " + preview);
            } else {
                System.out.println("⚠ 未获取到有效的图表数据");
            }

        } catch (Exception e) {
            System.err.println("解析图表数据失败: " + e.getMessage());
        }
    }

    /**
     * 分析煤炭指数相关元素
     */
    private static void analyzeCoalIndexElements(Page page) {
        try {
            System.out.println("\n--- 煤炭指数元素分析 ---");

            // 检查特定的煤炭指数相关选择器
            String[] coalSelectors = {
                ".coal-ing",
                "[class*='coal']",
                "[class*='price']",
                "[class*='index']",
                "[data-price]",
                "[data-value]",
                ".price-card",
                ".index-card",
                ".chart-container",
                ".data-container"
            };

            for (String selector : coalSelectors) {
                try {
                    Locator elements = page.locator(selector);
                    int count = elements.count();
                    if (count > 0) {
                        System.out.println("选择器 " + selector + " 找到 " + count + " 个元素");

                        // 输出前3个元素的内容
                        for (int i = 0; i < Math.min(count, 3); i++) {
                            try {
                                String text = elements.nth(i).textContent();
                                if (text != null && text.trim().length() > 0) {
                                    String preview = text.length() > 100 ?
                                        text.substring(0, 100) + "..." : text;
                                    System.out.println("  元素[" + i + "]: " + preview.trim());
                                }
                            } catch (Exception e) {
                                System.err.println("  获取元素[" + i + "]内容失败: " + e.getMessage());
                            }
                        }
                    }
                } catch (Exception e) {
                    // 忽略选择器错误
                }
            }

            // 检查页面中是否包含特定的煤炭指数关键词
            try {
                String pageContent = page.content();
                String[] keywords = {"神华外购", "CCI指数", "CCTD指数", "5500kCal", "5000kCal", "4500kCal"};

                System.out.println("\n--- 关键词检查 ---");
                for (String keyword : keywords) {
                    if (pageContent.contains(keyword)) {
                        System.out.println("✓ 发现关键词: " + keyword);
                    } else {
                        System.out.println("✗ 未发现关键词: " + keyword);
                    }
                }
            } catch (Exception e) {
                System.err.println("关键词检查失败: " + e.getMessage());
            }

        } catch (Exception e) {
            logger.error("分析煤炭指数元素失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 主方法，用于独立运行调试
     */
    public static void main(String[] args) {
        System.out.println("开始调试煤炭爬虫...");
        debugPageElements();
        System.out.println("调试结束");
    }
}
