package com.erdos.coal.crawler.util;

import com.erdos.coal.crawler.constants.JavaScriptConstants;
import com.erdos.coal.crawler.dto.CoalIndexDataDto;
import com.erdos.coal.crawler.enums.IndexType;
import com.microsoft.playwright.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 专门用于提取图表数据的工具类
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
public class ChartDataExtractor {
    
    private static final Logger logger = LoggerFactory.getLogger(ChartDataExtractor.class);
    private static final String BASE_URL = "https://www.meiyibao.com/";
    
    /**
     * 提取指定类型的煤炭指数图表数据
     */
    public static List<CoalIndexDataDto> extractChartData(IndexType indexType) {
        List<CoalIndexDataDto> resultList = new ArrayList<>();
        
        try (Playwright playwright = Playwright.create()) {
            Browser browser = playwright.chromium().launch(
                    new BrowserType.LaunchOptions().setHeadless(true)
            );
            
            BrowserContext context = browser.newContext(
                    new Browser.NewContextOptions()
                            .setUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
            );
            
            Page page = context.newPage();
            
            // 设置网络拦截器捕获AJAX请求
            setupNetworkCapture(page, resultList, indexType);
            
            // 导航到目标页面
            page.navigate(BASE_URL);
            
            // 等待页面加载
            page.waitForSelector(".coal-ing", new Page.WaitForSelectorOptions().setTimeout(30_000));
            
            // 点击对应的标签页
            clickIndexTab(page, indexType);
            
            // 等待数据加载
            page.waitForTimeout(30000);
            
            // 提取DOM中的数据
            resultList.addAll(extractDomData(page, indexType));
            
            // 提取图表数据
            resultList.addAll(extractJavaScriptData(page, indexType));
            
            page.close();
            context.close();
            browser.close();
            
        } catch (Exception e) {
            logger.error("提取图表数据失败: {}", e.getMessage(), e);
        }
        
        return resultList;
    }
    
    /**
     * 设置网络请求捕获
     */
    private static void setupNetworkCapture(Page page, List<CoalIndexDataDto> resultList, IndexType indexType) {
        page.onResponse(response -> {
            try {
                String url = response.url();
                String contentType = response.headers().get("content-type");
                
                // 检查是否是包含数据的响应
                if (isDataResponse(url, contentType)) {
                    String body = response.text();
                    if (body != null && containsCoalData(body)) {
                        logger.info("捕获到煤炭数据响应: {}", url);
                        resultList.addAll(parseNetworkData(body, indexType));
                    }
                }
            } catch (Exception e) {
                logger.debug("处理网络响应失败: {}", e.getMessage());
            }
        });
    }
    
    /**
     * 判断是否是数据响应
     */
    private static boolean isDataResponse(String url, String contentType) {
        if (url == null) return false;
        
        String lowerUrl = url.toLowerCase();
        boolean isDataUrl = lowerUrl.contains("api") || 
                           lowerUrl.contains("data") || 
                           lowerUrl.contains("chart") ||
                           lowerUrl.contains("ajax");
        
        boolean isJsonContent = contentType != null && contentType.contains("json");
        
        return isDataUrl || isJsonContent;
    }
    
    /**
     * 检查内容是否包含煤炭数据
     */
    private static boolean containsCoalData(String content) {
        if (content == null) return false;
        
        String lower = content.toLowerCase();
        return lower.contains("coal") || lower.contains("煤") ||
               lower.contains("price") || lower.contains("价格") ||
               lower.contains("5500") || lower.contains("5000") || lower.contains("4500") ||
               lower.contains("cctd") || lower.contains("cci");
    }
    
    /**
     * 解析网络数据
     */
    private static List<CoalIndexDataDto> parseNetworkData(String data, IndexType indexType) {
        List<CoalIndexDataDto> dataList = new ArrayList<>();
        
        try {
            // 使用正则表达式提取价格数据
            Pattern pricePattern = Pattern.compile("(\\d{3,})");
            Matcher matcher = pricePattern.matcher(data);
            
            while (matcher.find()) {
                try {
                    BigDecimal price = new BigDecimal(matcher.group(1));
                    
                    // 过滤合理的价格范围（300-800元）
                    if (price.compareTo(new BigDecimal("300")) >= 0 && 
                        price.compareTo(new BigDecimal("800")) <= 0) {
                        
                        CoalIndexDataDto dto = new CoalIndexDataDto();
                        dto.setIndexType(indexType);
                        dto.setPrice(price);
                        dto.setDataDate(new Date());
                        dto.setSourceUrl(BASE_URL);
                        
                        dataList.add(dto);
                    }
                } catch (Exception e) {
                    logger.debug("解析价格失败: {}", matcher.group(1));
                }
            }
            
        } catch (Exception e) {
            logger.error("解析网络数据失败: {}", e.getMessage(), e);
        }
        
        return dataList;
    }
    
    /**
     * 提取DOM数据
     */
    private static List<CoalIndexDataDto> extractDomData(Page page, IndexType indexType) {
        List<CoalIndexDataDto> dataList = new ArrayList<>();
        
        try {
            // 获取所有coal-ing元素
            Locator coalIngElements = page.locator(".coal-ing");
            int count = coalIngElements.count();
            
            for (int i = 0; i < count; i++) {
                try {
                    String text = coalIngElements.nth(i).textContent();
                    if (text != null && text.trim().length() > 0) {
                        CoalIndexDataDto data = parseTextData(text, indexType);
                        if (data != null) {
                            dataList.add(data);
                        }
                    }
                } catch (Exception e) {
                    logger.debug("解析DOM元素失败: {}", e.getMessage());
                }
            }
            
        } catch (Exception e) {
            logger.error("提取DOM数据失败: {}", e.getMessage(), e);
        }
        
        return dataList;
    }
    
    /**
     * 提取JavaScript数据
     */
    private static List<CoalIndexDataDto> extractJavaScriptData(Page page, IndexType indexType) {
        List<CoalIndexDataDto> dataList = new ArrayList<>();
        
        try {
            // 使用增强版JavaScript获取数据
            Object chartData = page.evaluate(JavaScriptConstants.GET_ALL_CHART_DATA);
            
            if (chartData != null) {
                String dataStr = chartData.toString();
                if (!dataStr.equals("null") && dataStr.length() > 0) {
                    dataList.addAll(parseChartDataString(dataStr, indexType));
                }
            }
            
        } catch (Exception e) {
            logger.error("提取JavaScript数据失败: {}", e.getMessage(), e);
        }
        
        return dataList;
    }
    
    /**
     * 解析文本数据
     */
    private static CoalIndexDataDto parseTextData(String text, IndexType indexType) {
        try {
            // 匹配价格和热值模式：如 "652元 5500kCal"
            Pattern pattern = Pattern.compile("(\\d+)元.*?(\\d+)kCal");
            Matcher matcher = pattern.matcher(text);
            
            if (matcher.find()) {
                BigDecimal price = new BigDecimal(matcher.group(1));
                Integer calorificValue = Integer.parseInt(matcher.group(2));
                
                CoalIndexDataDto data = new CoalIndexDataDto();
                data.setIndexType(indexType);
                data.setPrice(price);
                data.setCalorificValue(calorificValue);
                data.setDataDate(new Date());
                data.setSourceUrl(BASE_URL);
                
                return data;
            }
        } catch (Exception e) {
            logger.debug("解析文本数据失败: {}", text, e);
        }
        
        return null;
    }
    
    /**
     * 解析图表数据字符串
     */
    private static List<CoalIndexDataDto> parseChartDataString(String dataStr, IndexType indexType) {
        List<CoalIndexDataDto> dataList = new ArrayList<>();
        
        try {
            // 提取时间序列数据
            Pattern timeSeriesPattern = Pattern.compile("\"(\\d{2}-\\d{2})\"[^\\d]*(\\d{3,})");
            Matcher matcher = timeSeriesPattern.matcher(dataStr);
            
            while (matcher.find()) {
                try {
                    String dateStr = matcher.group(1);
                    BigDecimal price = new BigDecimal(matcher.group(2));
                    
                    CoalIndexDataDto data = new CoalIndexDataDto();
                    data.setIndexType(indexType);
                    data.setPrice(price);
                    data.setDataDate(parseDate(dateStr));
                    data.setSourceUrl(BASE_URL);
                    
                    dataList.add(data);
                } catch (Exception e) {
                    logger.debug("解析时间序列数据失败: {}", e.getMessage());
                }
            }
            
        } catch (Exception e) {
            logger.error("解析图表数据字符串失败: {}", e.getMessage(), e);
        }
        
        return dataList;
    }
    
    /**
     * 点击指数标签页
     */
    private static void clickIndexTab(Page page, IndexType indexType) {
        try {
            String tabSelector;
            switch (indexType) {
                case SHENHUA:
                    tabSelector = "#tab-1";
                    break;
                case CCI:
                    tabSelector = "#tab-2";
                    break;
                case CCTD:
                    tabSelector = "#tab-3";
                    break;
                default:
                    tabSelector = "#tab-3"; // 默认CCTD
            }
            
            page.querySelector(tabSelector).click();
            page.waitForTimeout(2000); // 等待标签页切换
            
        } catch (Exception e) {
            logger.error("点击标签页失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 解析日期
     */
    private static Date parseDate(String dateStr) {
        try {
            String[] parts = dateStr.split("-");
            if (parts.length == 2) {
                Calendar calendar = Calendar.getInstance();
                calendar.set(Calendar.MONTH, Integer.parseInt(parts[0]) - 1);
                calendar.set(Calendar.DAY_OF_MONTH, Integer.parseInt(parts[1]));
                return calendar.getTime();
            }
        } catch (Exception e) {
            logger.debug("解析日期失败: {}", dateStr);
        }
        return new Date();
    }
    
    /**
     * 测试方法
     */
    public static void main(String[] args) {
        System.out.println("开始提取CCTD指数数据...");
        
        List<CoalIndexDataDto> cctdData = extractChartData(IndexType.CCTD);
        System.out.println("CCTD数据条数: " + cctdData.size());

        for (CoalIndexDataDto data : cctdData) {
            System.out.println("价格: " + data.getPrice() +
                             ", 热值: " + data.getCalorificValue() +
                             ", 日期: " + data.getDataDate());
        }

        System.out.println("\n开始提取CCI指数数据...");
        List<CoalIndexDataDto> cciData = extractChartData(IndexType.CCI);
        System.out.println("CCI数据条数: " + cciData.size());

        System.out.println("\n开始提取神华外购数据...");
        List<CoalIndexDataDto> shenhuaData = extractChartData(IndexType.SHENHUA);
        System.out.println("神华外购数据条数: " + shenhuaData.size());
    }
}
