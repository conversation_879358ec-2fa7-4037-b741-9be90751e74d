package com.erdos.coal.crawler.util;

import com.microsoft.playwright.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * CCTD指数提取工具 - 专门针对CCTD指数的简化版本
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
public class CCTDExtractor {

    private static final Logger logger = LoggerFactory.getLogger(CCTDExtractor.class);
    private static final String BASE_URL = "https://www.meiyibao.com/";
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("MM-dd");
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        System.out.println("=== CCTD指数提取工具 ===");
        System.out.println("目标：提取7.22-7.30期间的CCTD指数数据");
        
        // 提取数据
        Map<String, Map<Integer, Integer>> data = extractCCTDData();
        
        // 打印结果
        printResults(data);
    }
    
    /**
     * 提取CCTD指数数据
     */
    public static Map<String, Map<Integer, Integer>> extractCCTDData() {
        Map<String, Map<Integer, Integer>> result = new LinkedHashMap<>();
        
        try (Playwright playwright = Playwright.create()) {
            // 启动浏览器，增加更多配置
            Browser browser = playwright.chromium().launch(
                    new BrowserType.LaunchOptions()
                            .setHeadless(false) // 可视化模式便于调试
                            .setTimeout(120000) // 增加到2分钟
                            .setArgs(java.util.Arrays.asList(
                                    "--no-sandbox",
                                    "--disable-dev-shm-usage",
                                    "--disable-blink-features=AutomationControlled",
                                    "--disable-web-security"
                            ))
            );

            BrowserContext context = browser.newContext(
                    new Browser.NewContextOptions()
                            .setUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
                            .setViewportSize(1920, 1080)
                            .setIgnoreHTTPSErrors(true) // 忽略HTTPS错误
            );

            Page page = context.newPage();
            page.setDefaultTimeout(120000); // 2分钟超时

            System.out.println("正在访问网站: " + BASE_URL);

            try {
                // 先尝试访问页面，使用更宽松的等待条件
                page.navigate(BASE_URL, new Page.NavigateOptions().setTimeout(120000));

                logger.info("页面导航成功，等待内容加载...");
                page.waitForTimeout(5000);

                // 检查页面是否加载成功
                String title = page.title();
                logger.info("页面标题: {}", title);

                // 尝试点击CCTD标签页
                try {
                    logger.info("尝试点击CCTD标签页...");
                    page.querySelector("#tab-3").click();
                    page.waitForTimeout(3000);
                    logger.info("CCTD标签页点击成功");
                } catch (Exception e) {
                    logger.warn("点击CCTD标签页失败，尝试其他方法: {}", e.getMessage());
                    // 尝试其他可能的选择器
                    try {
                        page.click("text=CCTD指数");
                        page.waitForTimeout(3000);
                    } catch (Exception e2) {
                        logger.warn("备用点击方法也失败: {}", e2.getMessage());
                    }
                }

                // 获取页面内容
                String pageContent = page.content();
                logger.info("获取到页面内容，长度: {}", pageContent.length());

                // 提取数据
                result = extractDataFromHTML(pageContent);

                // 如果没有数据，尝试直接从页面文本提取
                if (result.isEmpty()) {
                    logger.info("从HTML提取失败，尝试从页面文本提取...");
                    String pageText = page.textContent("body");
                    logger.info("页面文本长度: {}", pageText.length());
                    result = extractDataFromText(pageText);
                }

                // 如果还是没有数据，尝试模拟数据（用于测试）
                if (result.isEmpty()) {
                    logger.warn("所有提取方法都失败，生成模拟数据用于测试");
                    // result = generateMockData();
                }

            } catch (Exception e) {
                logger.error("页面操作失败: {}", e.getMessage(), e);
                // 即使页面访问失败，也生成一些模拟数据用于测试
                logger.info("生成模拟数据用于测试...");
                // result = generateMockData();
            }
            
            page.close();
            context.close();
            browser.close();
            
        } catch (Exception e) {
            logger.error("浏览器操作失败: {}", e.getMessage(), e);
        }
        
        return result;
    }

    /**
     * 生成模拟数据（用于测试）
     */
    private static Map<String, Map<Integer, Integer>> generateMockData() {
        Map<String, Map<Integer, Integer>> mockData = new LinkedHashMap<>();

        // 添加7.22-7.30的模拟数据
        String[] dates = {"07-22", "07-23", "07-24", "07-25", "07-26", "07-27", "07-28", "07-29", "07-30"};

        // 初始价格
        int price5500 = 643;
        int price5000 = 580;
        int price4500 = 514;

        for (String date : dates) {
            Map<Integer, Integer> dayData = new LinkedHashMap<>();
            dayData.put(5500, price5500);
            dayData.put(5000, price5000);
            dayData.put(4500, price4500);

            mockData.put(date, dayData);

            // 每天价格小幅变动
            price5500 += 2;
            price5000 += 2;
            price4500 += 2;
        }

        logger.info("生成了 {} 天的模拟数据", mockData.size());
        return mockData;
    }

    /**
     * 从HTML内容提取数据 - 改进版
     */
    private static Map<String, Map<Integer, Integer>> extractDataFromHTML(String html) {
        Map<String, Map<Integer, Integer>> result = new LinkedHashMap<>();

        try {
            logger.info("从HTML提取数据，HTML长度: {}", html.length());

            // 先检查HTML中是否包含关键信息
            if (!html.contains("07-") && !html.contains("kCal") && !html.contains("元")) {
                logger.warn("HTML中未找到关键信息，可能页面未正确加载");
                return result;
            }

            // 多种模式尝试提取数据

            // 模式1: 查找包含完整信息的行
            Pattern fullPattern = Pattern.compile("(07-\\d{2}).*?(\\d{4,5})kCal.*?(\\d{3,})");
            Matcher fullMatcher = fullPattern.matcher(html);

            while (fullMatcher.find()) {
                try {
                    String date = fullMatcher.group(1);
                    int calorific = Integer.parseInt(fullMatcher.group(2));
                    int price = Integer.parseInt(fullMatcher.group(3));

                    if (price >= 300 && price <= 800) { // 价格范围过滤
                        Map<Integer, Integer> dateData = result.computeIfAbsent(date, k -> new LinkedHashMap<>());
                        dateData.put(calorific, price);
                        logger.debug("模式1提取: {} - {}kCal={}元", date, calorific, price);
                    }
                } catch (Exception e) {
                    logger.debug("模式1解析失败: {}", e.getMessage());
                }
            }

            // 模式2: 分别查找日期和价格
            Pattern datePattern = Pattern.compile("(07-\\d{2})");
            Matcher dateMatcher = datePattern.matcher(html);

            while (dateMatcher.find()) {
                String date = dateMatcher.group(1);

                // 在日期前后200字符范围内查找价格信息
                int start = Math.max(0, dateMatcher.start() - 200);
                int end = Math.min(html.length(), dateMatcher.end() + 200);
                String context = html.substring(start, end);

                // 查找热值和价格
                Pattern pricePattern = Pattern.compile("(\\d{4,5})kCal[^\\d]*(\\d{3,})");
                Matcher priceMatcher = pricePattern.matcher(context);

                while (priceMatcher.find()) {
                    try {
                        int calorific = Integer.parseInt(priceMatcher.group(1));
                        int price = Integer.parseInt(priceMatcher.group(2));

                        if (price >= 300 && price <= 800) {
                            Map<Integer, Integer> dateData = result.computeIfAbsent(date, k -> new LinkedHashMap<>());
                            dateData.put(calorific, price);
                            logger.debug("模式2提取: {} - {}kCal={}元", date, calorific, price);
                        }
                    } catch (Exception e) {
                        logger.debug("模式2解析失败: {}", e.getMessage());
                    }
                }
            }

            // 模式3: 查找常见的价格模式
            Pattern priceOnlyPattern = Pattern.compile("(\\d{3})元.*?(\\d{4,5})kCal");
            Matcher priceOnlyMatcher = priceOnlyPattern.matcher(html);

            while (priceOnlyMatcher.find()) {
                try {
                    int price = Integer.parseInt(priceOnlyMatcher.group(1));
                    int calorific = Integer.parseInt(priceOnlyMatcher.group(2));

                    if (price >= 300 && price <= 800) {
                        // 假设是最近的日期
                        String defaultDate = "07-22";
                        Map<Integer, Integer> dateData = result.computeIfAbsent(defaultDate, k -> new LinkedHashMap<>());
                        dateData.put(calorific, price);
                        logger.debug("模式3提取: {} - {}kCal={}元", defaultDate, calorific, price);
                    }
                } catch (Exception e) {
                    logger.debug("模式3解析失败: {}", e.getMessage());
                }
            }

            logger.info("HTML提取完成，找到 {} 天的数据", result.size());

        } catch (Exception e) {
            logger.error("从HTML提取数据失败: {}", e.getMessage(), e);
        }

        return result;
    }
    
    /**
     * 从页面文本提取数据 - 改进版
     */
    private static Map<String, Map<Integer, Integer>> extractDataFromText(String text) {
        Map<String, Map<Integer, Integer>> result = new LinkedHashMap<>();

        try {
            logger.info("从文本提取数据，文本长度: {}", text.length());

            // 先检查文本中是否包含关键信息
            if (!text.contains("07-") && !text.contains("kCal") && !text.contains("元")) {
                logger.warn("文本中未找到关键信息");
                return result;
            }

            // 输出文本的前500字符用于调试
            String preview = text.length() > 500 ? text.substring(0, 500) + "..." : text;
            logger.debug("文本预览: {}", preview);

            // 按行分割
            String[] lines = text.split("\n");
            logger.info("文本共 {} 行", lines.length);

            for (int i = 0; i < lines.length; i++) {
                String line = lines[i].trim();
                if (line.isEmpty()) continue;

                // 查找包含日期的行
                if (line.contains("07-")) {
                    logger.debug("发现包含日期的行[{}]: {}", i, line);

                    // 提取日期
                    Pattern datePattern = Pattern.compile("(07-\\d{2})");
                    Matcher dateMatcher = datePattern.matcher(line);

                    while (dateMatcher.find()) {
                        String date = dateMatcher.group(1);

                        // 在当前行和前后几行中查找价格信息
                        String context = line;

                        // 扩展上下文（前后2行）
                        for (int j = Math.max(0, i-2); j <= Math.min(lines.length-1, i+2); j++) {
                            if (j != i) {
                                context += " " + lines[j].trim();
                            }
                        }

                        // 查找热值和价格
                        Pattern pricePattern = Pattern.compile("(\\d{4,5})kCal[^\\d]*(\\d{3,})");
                        Matcher priceMatcher = pricePattern.matcher(context);

                        while (priceMatcher.find()) {
                            try {
                                int calorific = Integer.parseInt(priceMatcher.group(1));
                                int price = Integer.parseInt(priceMatcher.group(2));

                                if (price >= 300 && price <= 800) {
                                    Map<Integer, Integer> dateData = result.computeIfAbsent(date, k -> new LinkedHashMap<>());
                                    dateData.put(calorific, price);
                                    logger.debug("从文本提取: {} - {}kCal={}元", date, calorific, price);
                                }
                            } catch (Exception e) {
                                logger.debug("解析价格失败: {}", e.getMessage());
                            }
                        }

                        // 如果没有找到完整的价格信息，尝试查找单独的数字
                        if (!result.containsKey(date)) {
                            Pattern numberPattern = Pattern.compile("\\b(\\d{3})\\b");
                            Matcher numberMatcher = numberPattern.matcher(context);

                            while (numberMatcher.find()) {
                                try {
                                    int price = Integer.parseInt(numberMatcher.group(1));

                                    if (price >= 300 && price <= 800) {
                                        Map<Integer, Integer> dateData = result.computeIfAbsent(date, k -> new LinkedHashMap<>());
                                        dateData.put(5500, price); // 默认5500kCal
                                        logger.debug("从文本提取简单价格: {} - 5500kCal={}元", date, price);
                                        break; // 只取第一个合理的价格
                                    }
                                } catch (Exception e) {
                                    logger.debug("解析简单价格失败: {}", e.getMessage());
                                }
                            }
                        }
                    }
                }

                // 查找包含热值和价格但没有日期的行
                if (line.contains("kCal") && (line.contains("元") || line.matches(".*\\d{3,}.*"))) {
                    logger.debug("发现包含价格的行[{}]: {}", i, line);

                    Pattern pricePattern = Pattern.compile("(\\d{4,5})kCal[^\\d]*(\\d{3,})");
                    Matcher priceMatcher = pricePattern.matcher(line);

                    while (priceMatcher.find()) {
                        try {
                            int calorific = Integer.parseInt(priceMatcher.group(1));
                            int price = Integer.parseInt(priceMatcher.group(2));

                            if (price >= 300 && price <= 800) {
                                // 使用默认日期
                                String defaultDate = "07-22";
                                Map<Integer, Integer> dateData = result.computeIfAbsent(defaultDate, k -> new LinkedHashMap<>());
                                dateData.put(calorific, price);
                                logger.debug("从价格行提取: {} - {}kCal={}元", defaultDate, calorific, price);
                            }
                        } catch (Exception e) {
                            logger.debug("解析价格行失败: {}", e.getMessage());
                        }
                    }
                }
            }

            logger.info("文本提取完成，找到 {} 天的数据", result.size());

        } catch (Exception e) {
            logger.error("从文本提取数据失败: {}", e.getMessage(), e);
        }

        return result;
    }
    
    /**
     * 打印结果
     */
    private static void printResults(Map<String, Map<Integer, Integer>> data) {
        System.out.println("\n=== CCTD指数数据 ===");
        
        if (data.isEmpty()) {
            System.out.println("未提取到数据！");
            return;
        }
        
        // 按日期排序
        List<String> dates = new ArrayList<>(data.keySet());
        Collections.sort(dates);
        
        for (String date : dates) {
            Map<Integer, Integer> prices = data.get(date);
            
            System.out.print(date + ": ");
            
            List<String> priceStrings = new ArrayList<>();
            for (Map.Entry<Integer, Integer> entry : prices.entrySet()) {
                priceStrings.add(entry.getKey() + "kCal=" + entry.getValue() + "元");
            }
            
            System.out.println(String.join(", ", priceStrings));
        }
        
        System.out.println("\n总共提取到 " + data.size() + " 天的数据");
    }
}
