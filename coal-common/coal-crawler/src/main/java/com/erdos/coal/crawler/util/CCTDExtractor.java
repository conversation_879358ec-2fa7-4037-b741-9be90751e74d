package com.erdos.coal.crawler.util;

import com.erdos.coal.crawler.dto.CoalIndexDataDto;
import com.erdos.coal.crawler.enums.IndexType;
import com.microsoft.playwright.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * CCTD指数提取工具 - 专门针对CCTD指数的简化版本
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
public class CCTDExtractor {
    
    private static final Logger logger = LoggerFactory.getLogger(CCTDExtractor.class);
    private static final String BASE_URL = "https://www.meiyibao.com/";
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("MM-dd");
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        System.out.println("=== CCTD指数提取工具 ===");
        System.out.println("目标：提取7.22-7.30期间的CCTD指数数据");
        
        // 提取数据
        Map<String, Map<Integer, Integer>> data = extractCCTDData();
        
        // 打印结果
        printResults(data);
    }
    
    /**
     * 提取CCTD指数数据
     */
    public static Map<String, Map<Integer, Integer>> extractCCTDData() {
        Map<String, Map<Integer, Integer>> result = new LinkedHashMap<>();
        
        try (Playwright playwright = Playwright.create()) {
            Browser browser = playwright.chromium().launch(
                    new BrowserType.LaunchOptions()
                            .setHeadless(false) // 可视化模式便于调试
                            .setTimeout(60000)
            );
            
            BrowserContext context = browser.newContext(
                    new Browser.NewContextOptions()
                            .setUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
                            .setViewportSize(1920, 1080)
            );
            
            Page page = context.newPage();
            page.setDefaultTimeout(60000);
            
            logger.info("正在访问网站: {}", BASE_URL);
            
            try {
                // 导航到目标页面
                page.navigate(BASE_URL);
                
                logger.info("页面导航成功，等待内容加载...");
                page.waitForTimeout(3000);
                
                // 点击CCTD标签页
                logger.info("点击CCTD标签页...");
                page.querySelector("#tab-3").click();
                page.waitForTimeout(3000);
                
                // 获取页面内容
                String pageContent = page.content();
                
                // 提取数据
                result = extractDataFromHTML(pageContent);
                
                // 如果没有数据，尝试直接从页面文本提取
                if (result.isEmpty()) {
                    logger.info("从HTML提取失败，尝试从页面文本提取...");
                    String pageText = page.textContent("body");
                    result = extractDataFromText(pageText);
                }
                
            } catch (Exception e) {
                logger.error("页面操作失败: {}", e.getMessage(), e);
            }
            
            page.close();
            context.close();
            browser.close();
            
        } catch (Exception e) {
            logger.error("浏览器操作失败: {}", e.getMessage(), e);
        }
        
        return result;
    }
    
    /**
     * 从HTML内容提取数据
     */
    private static Map<String, Map<Integer, Integer>> extractDataFromHTML(String html) {
        Map<String, Map<Integer, Integer>> result = new LinkedHashMap<>();
        
        try {
            logger.info("从HTML提取数据，HTML长度: {}", html.length());
            
            // 查找日期和价格模式
            // 模式1: 日期和价格在同一行
            Pattern pattern1 = Pattern.compile("(07-\\d{2}).*?(\\d{4,5})kCal.*?(\\d{3})");
            Matcher matcher1 = pattern1.matcher(html);
            
            while (matcher1.find()) {
                String date = matcher1.group(1);
                int calorific = Integer.parseInt(matcher1.group(2));
                int price = Integer.parseInt(matcher1.group(3));
                
                Map<Integer, Integer> dateData = result.computeIfAbsent(date, k -> new LinkedHashMap<>());
                dateData.put(calorific, price);
                
                logger.debug("提取到数据: {} - {}kCal={}元", date, calorific, price);
            }
            
            // 模式2: 日期和价格分开
            Pattern datePattern = Pattern.compile("(07-\\d{2})");
            Matcher dateMatcher = datePattern.matcher(html);
            
            while (dateMatcher.find()) {
                String date = dateMatcher.group(1);
                
                // 在日期附近查找价格和热值
                int start = Math.max(0, dateMatcher.start() - 100);
                int end = Math.min(html.length(), dateMatcher.end() + 100);
                String context = html.substring(start, end);
                
                Pattern pricePattern = Pattern.compile("(\\d{4,5})kCal.*?(\\d{3})");
                Matcher priceMatcher = pricePattern.matcher(context);
                
                while (priceMatcher.find()) {
                    int calorific = Integer.parseInt(priceMatcher.group(1));
                    int price = Integer.parseInt(priceMatcher.group(2));
                    
                    Map<Integer, Integer> dateData = result.computeIfAbsent(date, k -> new LinkedHashMap<>());
                    dateData.put(calorific, price);
                    
                    logger.debug("提取到数据(模式2): {} - {}kCal={}元", date, calorific, price);
                }
            }
            
        } catch (Exception e) {
            logger.error("从HTML提取数据失败: {}", e.getMessage(), e);
        }
        
        return result;
    }
    
    /**
     * 从页面文本提取数据
     */
    private static Map<String, Map<Integer, Integer>> extractDataFromText(String text) {
        Map<String, Map<Integer, Integer>> result = new LinkedHashMap<>();
        
        try {
            logger.info("从文本提取数据，文本长度: {}", text.length());
            
            // 按行分割
            String[] lines = text.split("\n");
            
            for (String line : lines) {
                line = line.trim();
                if (line.isEmpty()) continue;
                
                // 查找包含日期的行
                if (line.contains("07-")) {
                    // 提取日期
                    Pattern datePattern = Pattern.compile("(07-\\d{2})");
                    Matcher dateMatcher = datePattern.matcher(line);
                    
                    if (dateMatcher.find()) {
                        String date = dateMatcher.group(1);
                        
                        // 查找热值和价格
                        Pattern pricePattern = Pattern.compile("(\\d{4,5})kCal.*?(\\d{3})");
                        Matcher priceMatcher = pricePattern.matcher(line);
                        
                        if (priceMatcher.find()) {
                            int calorific = Integer.parseInt(priceMatcher.group(1));
                            int price = Integer.parseInt(priceMatcher.group(2));
                            
                            Map<Integer, Integer> dateData = result.computeIfAbsent(date, k -> new LinkedHashMap<>());
                            dateData.put(calorific, price);
                            
                            logger.debug("从文本行提取数据: {} - {}kCal={}元", date, calorific, price);
                        } else {
                            // 如果同一行没有价格，尝试查找数字
                            Pattern numberPattern = Pattern.compile("\\b(\\d{3})\\b");
                            Matcher numberMatcher = numberPattern.matcher(line);
                            
                            if (numberMatcher.find()) {
                                int price = Integer.parseInt(numberMatcher.group(1));
                                
                                // 假设是5500kCal的价格
                                Map<Integer, Integer> dateData = result.computeIfAbsent(date, k -> new LinkedHashMap<>());
                                dateData.put(5500, price);
                                
                                logger.debug("从文本行提取简单价格: {} - 5500kCal={}元", date, price);
                            }
                        }
                    }
                }
                
                // 查找包含热值和价格的行
                if (line.contains("kCal") && line.contains("元")) {
                    Pattern pricePattern = Pattern.compile("(\\d{4,5})kCal.*?(\\d{3})元");
                    Matcher priceMatcher = pricePattern.matcher(line);
                    
                    if (priceMatcher.find()) {
                        int calorific = Integer.parseInt(priceMatcher.group(1));
                        int price = Integer.parseInt(priceMatcher.group(2));
                        
                        // 假设是最近的日期
                        String date = "07-22"; // 默认日期
                        
                        Map<Integer, Integer> dateData = result.computeIfAbsent(date, k -> new LinkedHashMap<>());
                        dateData.put(calorific, price);
                        
                        logger.debug("从价格行提取数据: {} - {}kCal={}元", date, calorific, price);
                    }
                }
            }
            
        } catch (Exception e) {
            logger.error("从文本提取数据失败: {}", e.getMessage(), e);
        }
        
        return result;
    }
    
    /**
     * 打印结果
     */
    private static void printResults(Map<String, Map<Integer, Integer>> data) {
        System.out.println("\n=== CCTD指数数据 ===");
        
        if (data.isEmpty()) {
            System.out.println("未提取到数据！");
            return;
        }
        
        // 按日期排序
        List<String> dates = new ArrayList<>(data.keySet());
        Collections.sort(dates);
        
        for (String date : dates) {
            Map<Integer, Integer> prices = data.get(date);
            
            System.out.print(date + ": ");
            
            List<String> priceStrings = new ArrayList<>();
            for (Map.Entry<Integer, Integer> entry : prices.entrySet()) {
                priceStrings.add(entry.getKey() + "kCal=" + entry.getValue() + "元");
            }
            
            System.out.println(String.join(", ", priceStrings));
        }
        
        System.out.println("\n总共提取到 " + data.size() + " 天的数据");
    }
}
