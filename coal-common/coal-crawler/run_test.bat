@echo off
echo ===== 煤炭数据爬虫测试 =====
echo.

echo 1. 编译项目...
call mvn compile
if %errorlevel% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo.
echo 2. 运行图表数据提取器测试...
java -cp "target/classes;target/dependency/*" com.erdos.coal.crawler.util.ChartDataExtractor

echo.
echo 3. 如需运行调试工具，请手动执行：
echo java -cp "target/classes;target/dependency/*" com.erdos.coal.crawler.util.CrawlerDebugUtil

echo.
echo 4. 如需运行完整测试，请手动执行：
echo java -cp "target/classes;target/dependency/*" com.erdos.coal.crawler.test.CoalDataCrawlerTest

echo.
echo 测试完成！
pause
